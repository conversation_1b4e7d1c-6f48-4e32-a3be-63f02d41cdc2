/**
 * AES-ECB-PKCS5Padding 加密工具
 *
 * 使用方法:
 * 1. 批量加密模式（默认）: node encrypt.js [密钥]
 *    - 读取output目录下的文件，加密后输出到input目录
 *    - 如果不提供密钥，会提示输入
 *
 * 2. 交互式单文件加密模式: node encrypt.js --interactive
 *    - 手动输入文本内容进行加密
 *
 * 与decrypt.js保持一致的加密/解密方式
 */

const crypto = require('crypto');
const readline = require('readline');
const fs = require('fs');
const path = require('path');

/**
 * AES-ECB-PKCS5Padding加密函数
 * @param {string|Buffer} plainData - 明文数据
 * @param {string} key - 加密密钥
 * @returns {Buffer} - 加密后的数据
 */
function encryptAES(plainData, key) {
  try {
    // 确保明文是Buffer类型
    const plainBuffer = Buffer.isBuffer(plainData) 
      ? plainData 
      : Buffer.from(plainData, 'utf8');
      
    // 确保密钥长度为16、24或32字节（对应128、192或256位AES）
    const keyBuffer = ensureValidKeyLength(key);
    
    // 使用ECB模式创建加密器
    const keyLength = keyBuffer.length * 8; // 转换为位长度
    const algorithm = `aes-${keyLength}-ecb`;
    
    // 创建加密器
    const cipher = crypto.createCipheriv(algorithm, keyBuffer, null); // ECB模式不需要IV
    
    // 设置自动填充（PKCS5Padding）
    cipher.setAutoPadding(true);
    
    // 加密
    const encryptedPart1 = cipher.update(plainBuffer);
    const encryptedPart2 = cipher.final();
    
    // 合并加密结果
    return Buffer.concat([encryptedPart1, encryptedPart2]);
  } catch (error) {
    console.error('加密失败:', error.message);
    return null;
  }
}

/**
 * 确保密钥长度为16、24或32字节
 * @param {string} key - 原始密钥
 * @returns {Buffer} - 调整后的密钥Buffer
 */
function ensureValidKeyLength(key) {
  const keyBuffer = Buffer.from(key, 'utf8');
  
  // 有效的AES密钥长度（字节）
  const validLengths = [16, 24, 32];
  
  // 如果密钥长度已经符合要求，直接返回
  if (validLengths.includes(keyBuffer.length)) {
    return keyBuffer;
  }
  
  // 根据密钥长度调整
  if (keyBuffer.length < 16) {
    // 如果太短，填充到16字节
    const newBuffer = Buffer.alloc(16, 0);
    keyBuffer.copy(newBuffer);
    return newBuffer;
  } else if (keyBuffer.length < 24) {
    // 如果在16-24之间，截取或填充到16字节
    return keyBuffer.slice(0, 16);
  } else if (keyBuffer.length < 32) {
    // 如果在24-32之间，截取或填充到24字节
    return keyBuffer.slice(0, 24);
  } else {
    // 如果超过32字节，截取到32字节
    return keyBuffer.slice(0, 32);
  }
}

/**
 * 确保目录存在，如果不存在则创建
 * @param {string} dirPath - 目录路径
 */
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`创建目录: ${dirPath}`);
  }
}

/**
 * 处理命令行参数
 * @returns {Object} - 解析后的参数
 */
function parseArguments() {
  const args = process.argv.slice(2);
  const params = {};
  
  // 检查是否提供了密钥
  if (args.length === 0) {
    console.error('请提供加密密钥作为命令行参数');
    console.error('用法: node encrypt.js <密钥>');
    process.exit(1);
  }
  
  // 第一个参数作为密钥
  params.key = args[0];
  
  return params;
}

/**
 * 交互式加密模式（原有功能保留）
 */
async function interactiveMode() {
  // 创建命令行交互界面
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  // 确保input目录存在
  const inputDir = path.join(__dirname, 'input');
  ensureDirectoryExists(inputDir);
  
  // 提示用户输入
  console.log('AES-ECB-PKCS5Padding 加密示例');
  console.log('----------------------------');
  console.log('加密结果将保存到input目录，以便使用decrypt.js进行解密测试');

  // 获取明文
  rl.question('请输入要加密的文本: ', (plainText) => {
    // 获取密钥
    rl.question('请输入加密密钥: ', (key) => {
      // 获取文件名
      rl.question('请输入保存的文件名: ', (fileName) => {
        // 执行加密
        const encryptedData = encryptAES(plainText, key);
        
        if (encryptedData) {
          // 保存到文件
          const filePath = path.join(inputDir, fileName);
          fs.writeFileSync(filePath, encryptedData);
          
          console.log(`\n加密成功，文件已保存到: ${filePath}`);
          console.log('\n加密结果 (Base64编码, 仅供参考):');
          console.log(encryptedData.toString('base64'));
          
          console.log('\n解密命令:');
          console.log(`node decrypt.js "${key}"`);
        }
        
        rl.close();
      });
    });
  });
}

/**
 * 批量加密模式 - 读取output目录下的文件进行加密
 */
async function batchEncryptMode(key) {
  // 确保output和input目录存在
  const outputDir = path.join(__dirname, 'output');
  const inputDir = path.join(__dirname, 'input');
  
  ensureDirectoryExists(outputDir);
  ensureDirectoryExists(inputDir);
  
  // 读取output目录下的所有文件
  try {
    const files = fs.readdirSync(outputDir);
    
    if (files.length === 0) {
      console.log(`output目录为空，请将需要加密的文件放在 ${outputDir} 目录下`);
      return;
    }
    
    console.log(`发现 ${files.length} 个待加密文件`);
    
    // 处理每个文件
    for (const file of files) {
      const outputFilePath = path.join(outputDir, file);
      const inputFilePath = path.join(inputDir, file);
      
      // 检查文件状态
      const stats = fs.statSync(outputFilePath);
      if (!stats.isFile()) {
        console.log(`跳过非文件项: ${file}`);
        continue;
      }
      
      console.log(`处理文件: ${file}`);
      
      try {
        // 读取文件内容（二进制模式）
        const fileContent = fs.readFileSync(outputFilePath);
        
        // 加密数据
        const encryptedData = encryptAES(fileContent, key);
        
        if (!encryptedData) {
          console.error(`无法加密文件: ${file}`);
          continue;
        }
        
        // 写入加密后的数据（二进制格式，与decrypt.js的处理方式一致）
        fs.writeFileSync(inputFilePath, encryptedData);
        console.log(`文件已加密并保存至: ${inputFilePath}`);
        
        // 显示Base64编码供参考
        console.log(`Base64编码 (仅供参考): ${encryptedData.toString('base64').substring(0, 50)}...`);
        
      } catch (error) {
        console.error(`处理文件 ${file} 时出错:`, error.message);
      }
    }
    
    console.log('所有文件处理完成');
    console.log(`\n解密命令: node decrypt.js "${key}"`);
  } catch (error) {
    console.error('处理文件时出错:', error.message);
  }
}

/**
 * 批量加密模式 - 交互式获取密钥
 */
async function batchEncryptModeInteractive() {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    rl.question('请输入加密密钥: ', async (key) => {
      rl.close();
      if (!key.trim()) {
        console.error('密钥不能为空');
        process.exit(1);
      }
      await batchEncryptMode(key.trim());
      resolve();
    });
  });
}

/**
 * 主函数
 */
async function main() {
  // 检查是否提供了命令行参数
  const args = process.argv.slice(2);

  // 检查是否有 --interactive 或 -i 参数强制使用交互式模式
  const forceInteractive = args.includes('--interactive') || args.includes('-i');

  if (forceInteractive) {
    // 强制使用交互式模式
    console.log('启动交互式加密模式...\n');
    await interactiveMode();
  } else if (args.length === 0) {
    // 没有参数，默认使用批量加密模式（交互式获取密钥）
    console.log('启动批量加密模式...\n');
    console.log('将读取 output 目录下的文件，加密后保存到 input 目录');
    console.log('如需使用交互式单文件加密模式，请运行: node encrypt.js --interactive\n');
    await batchEncryptModeInteractive();
  } else {
    // 有参数，使用批量加密模式（命令行密钥）
    const params = parseArguments();
    console.log('启动批量加密模式...\n');
    await batchEncryptMode(params.key);
  }
}

// 执行主函数
main().catch(error => {
  console.error('发生错误:', error);
  process.exit(1);
});

// 添加导出以便其他模块可以使用
module.exports = {
  encryptAES,
  ensureValidKeyLength
}; 

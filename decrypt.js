/**
 * AES-ECB-PKCS5Padding 解密工具
 * 使用Node.js内置的crypto模块实现
 * 读取input目录下的文件，解密后输出到output目录
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

/**
 * AES-ECB-PKCS5Padding解密函数
 * @param {Buffer|string} encryptedData - 加密的数据（Buffer或Base64字符串）
 * @param {string} key - 解密密钥
 * @returns {Buffer} - 解密后的数据
 */
function decryptAES(encryptedData, key) {
  try {
    // 确保密钥长度为16、24或32字节（对应128、192或256位AES）
    const keyBuffer = ensureValidKeyLength(key);
    
    // 使用ECB模式创建解密器
    // 注意：Node.js中，ECB模式被表示为"aes-xxx-ecb"
    const keyLength = keyBuffer.length * 8; // 转换为位长度
    const algorithm = `aes-${keyLength}-ecb`;
    
    // 创建解密器
    const decipher = crypto.createDecipheriv(algorithm, keyBuffer, null);  // ECB模式不需要IV，传null
    
    // 设置自动填充（PKCS5Padding）
    decipher.setAutoPadding(true);
    
    // 处理输入，确保它是Buffer类型
    const encryptedBuffer = Buffer.isBuffer(encryptedData) 
      ? encryptedData 
      : Buffer.from(encryptedData, 'base64');
    
    // 解密
    let decrypted = decipher.update(encryptedBuffer);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    
    return decrypted;
  } catch (error) {
    console.error('解密失败:', error.message);
    return null;
  }
}

/**
 * 确保密钥长度为16、24或32字节
 * @param {string} key - 原始密钥
 * @returns {Buffer} - 调整后的密钥Buffer
 */
function ensureValidKeyLength(key) {
  const keyBuffer = Buffer.from(key, 'utf8');
  
  // 有效的AES密钥长度（字节）
  const validLengths = [16, 24, 32];
  
  // 如果密钥长度已经符合要求，直接返回
  if (validLengths.includes(keyBuffer.length)) {
    return keyBuffer;
  }
  
  // 根据密钥长度调整
  if (keyBuffer.length < 16) {
    // 如果太短，填充到16字节
    const newBuffer = Buffer.alloc(16, 0);
    keyBuffer.copy(newBuffer);
    return newBuffer;
  } else if (keyBuffer.length < 24) {
    // 如果在16-24之间，截取或填充到16字节
    return keyBuffer.slice(0, 16);
  } else if (keyBuffer.length < 32) {
    // 如果在24-32之间，截取或填充到24字节
    return keyBuffer.slice(0, 24);
  } else {
    // 如果超过32字节，截取到32字节
    return keyBuffer.slice(0, 32);
  }
}

/**
 * 确保目录存在，如果不存在则创建
 * @param {string} dirPath - 目录路径
 */
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`创建目录: ${dirPath}`);
  }
}

/**
 * 处理命令行参数
 * @returns {Object} - 解析后的参数
 */
function parseArguments() {
  const args = process.argv.slice(2);
  const params = {};
  
  // 检查是否提供了密钥
  if (args.length === 0) {
    console.error('请提供解密密钥作为命令行参数');
    console.error('用法: node decrypt.js <密钥>');
    process.exit(1);
  }
  
  // 第一个参数作为密钥
  params.key = args[0];
  
  return params;
}

/**
 * 主函数
 */
async function main() {
  // 解析命令行参数
  const params = parseArguments();
  
  // 确保input和output目录存在
  const inputDir = path.join(__dirname, 'input');
  const outputDir = path.join(__dirname, 'output');
  
  ensureDirectoryExists(inputDir);
  ensureDirectoryExists(outputDir);
  
  // 读取input目录下的所有文件
  try {
    const files = fs.readdirSync(inputDir);
    
    if (files.length === 0) {
      console.log(`input目录为空，请将需要解密的文件放在 ${inputDir} 目录下`);
      return;
    }
    
    console.log(`发现 ${files.length} 个待解密文件`);
    
    // 处理每个文件
    for (const file of files) {
      const inputFilePath = path.join(inputDir, file);
      const outputFilePath = path.join(outputDir, file);
      
      // 检查文件状态
      const stats = fs.statSync(inputFilePath);
      if (!stats.isFile()) {
        console.log(`跳过非文件项: ${file}`);
        continue;
      }
      
      console.log(`处理文件: ${file}`);
      
      // 读取文件内容 - 假定是文本文件，包含Base64编码的加密数据
      const fileContent = fs.readFileSync(inputFilePath, 'utf8');
      
      try {
        // 首先尝试将文件内容视为Base64编码的加密数据
        const encryptedData = fileContent.trim();
        
        // 解密数据
        const decryptedData = decryptAES(encryptedData, params.key);
        
        if (!decryptedData) {
          console.error(`无法解密文件: ${file}`);
          continue;
        }
        
        // 写入解密后的数据
        fs.writeFileSync(outputFilePath, decryptedData);
        console.log(`文件已解密并保存至: ${outputFilePath}`);
      } catch (error) {
        console.error(`处理文件 ${file} 时出错:`, error.message);
        
        // 如果第一种方式失败，尝试直接解密二进制数据
        try {
          console.log(`尝试直接解密二进制数据...`);
          const encryptedBinary = fs.readFileSync(inputFilePath);
          const decryptedData = decryptAES(encryptedBinary, params.key);
          
          if (!decryptedData) {
            console.error(`无法解密文件: ${file}`);
            continue;
          }
          
          fs.writeFileSync(outputFilePath, decryptedData);
          console.log(`文件已解密并保存至: ${outputFilePath}`);
        } catch (binaryError) {
          console.error(`所有解密方式都失败: ${file}`);
        }
      }
    }
    
    console.log('所有文件处理完成');
  } catch (error) {
    console.error('处理文件时出错:', error.message);
  }
}

// 执行主函数
main().catch(error => {
  console.error('发生错误:', error);
  process.exit(1);
});

// 添加导出以便其他模块可以使用
module.exports = {
  decryptAES
}; 
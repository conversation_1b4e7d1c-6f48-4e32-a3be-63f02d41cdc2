# AES 解密工具

这是一个使用 Node.js 实现的 AES 解密工具，支持 ECB 模式和 PKCS5Padding 填充方式。

## 功能特点

- AES-ECB 模式解密
- PKCS5Padding 填充
- 自动处理密钥长度（128/192/256位）
- 批量处理文件
- 支持解密Base64编码的加密数据
- 命令行界面

## 目录结构

```
.
├── decrypt.js      # 解密脚本（批量处理input目录下的文件）
├── decode_base64.js # Base64解码辅助脚本（处理单个文件）
├── encrypt.js      # 加密示例（用于测试）
├── input/          # 存放待解密的文件
└── output/         # 存放解密后的文件
```

## 使用方法

### 方法1：批量解密目录中的文件

1. 将需要解密的文件放入 `input` 目录
2. 运行解密脚本，并提供解密密钥：

```bash
node decrypt.js <密钥>
```

3. 解密后的文件将保存到 `output` 目录

### 方法2：解密单个Base64编码文件

如果您的文件是Base64编码的加密数据，可以使用专门的解码脚本：

```bash
node decode_base64.js <密钥> <输入文件路径> [输出文件路径]
```

如果不指定输出文件路径，解密结果将保存到 `output` 目录，使用与输入文件相同的文件名。

### 测试加密

为了方便测试，提供了一个加密示例脚本：

```bash
node encrypt.js
```

按照提示输入明文、密钥和文件名，加密后的文件将保存到 `input` 目录。

## 注意事项

- 密钥长度会自动调整为 16/24/32 字节（对应 AES-128/192/256）
- 支持处理任何二进制文件
- 支持解密Base64编码的加密数据
- 如果解密失败，请确保使用了正确的密钥和合适的解密方法
- ECB 模式在某些情况下不安全，仅供测试使用

## 依赖

- Node.js v10.0.0 或更高版本
- 仅使用内置模块（crypto, fs, path） 